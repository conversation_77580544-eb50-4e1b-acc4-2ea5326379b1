import { Chroma<PERSON>pi, OpenAIEmbeddingFunction, Collection } from 'chromadb';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LocalVectorService {
  private client: ChromaApi;
  private embeddingFunction: OpenAIEmbeddingFunction;
  private collection: Collection;

  constructor() {
    this.initializeChroma();
  }

  private async initializeChroma() {
    try {
      // Initialize Chroma client (local instance)
      this.client = new ChromaApi({
        path: process.env.CHROMA_DB_PATH || './chroma_db'
      });

      // Initialize embedding function
      this.embeddingFunction = new OpenAIEmbeddingFunction({
        openai_api_key: process.env.OPENAI_API_KEY,
        openai_model: "text-embedding-ada-002"
      });

      // Get or create collection
      this.collection = await this.client.getOrCreateCollection({
        name: "chatai_documents",
        embeddingFunction: this.embeddingFunction,
      });

      console.log('✅ Local Vector DB (Chroma) initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Chroma DB:', error);
      throw error;
    }
  }

  /**
   * Add document to local vector database
   */
  async addDocument(
    documentId: string,
    parsedText: string,
    metadata: any
  ): Promise<string> {
    try {
      console.log(`📝 Adding document ${documentId} to local vector DB`);

      // Split text into chunks (simple implementation)
      const chunks = this.splitTextIntoChunks(parsedText, 1000);
      
      const ids: string[] = [];
      const documents: string[] = [];
      const metadatas: any[] = [];

      chunks.forEach((chunk, index) => {
        const chunkId = `${documentId}_chunk_${index}`;
        ids.push(chunkId);
        documents.push(chunk);
        metadatas.push({
          ...metadata,
          documentId,
          chunkIndex: index,
          chunkId
        });
      });

      // Add to collection
      await this.collection.add({
        ids,
        documents,
        metadatas
      });

      console.log(`✅ Added ${chunks.length} chunks for document ${documentId}`);
      return `local_${documentId}`;
    } catch (error) {
      console.error(`❌ Failed to add document ${documentId}:`, error);
      throw error;
    }
  }

  /**
   * Query local vector database
   */
  async queryDocuments(
    query: string,
    documentIds?: string[],
    topK: number = 5
  ): Promise<any> {
    try {
      console.log(`🔍 Querying local vector DB: "${query.substring(0, 50)}..."`);

      const whereClause = documentIds ? {
        documentId: { $in: documentIds }
      } : undefined;

      const results = await this.collection.query({
        queryTexts: [query],
        nResults: topK,
        where: whereClause
      });

      console.log(`✅ Found ${results.documents[0]?.length || 0} relevant chunks`);

      return {
        nodes: results.documents[0]?.map((doc, index) => ({
          text: doc,
          score: results.distances?.[0]?.[index] || 0,
          metadata: results.metadatas?.[0]?.[index] || {}
        })) || []
      };
    } catch (error) {
      console.error('❌ Failed to query local vector DB:', error);
      throw error;
    }
  }

  /**
   * Delete document from local vector database
   */
  async deleteDocument(documentId: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting document ${documentId} from local vector DB`);

      await this.collection.delete({
        where: { documentId }
      });

      console.log(`✅ Deleted document ${documentId} from local vector DB`);
    } catch (error) {
      console.error(`❌ Failed to delete document ${documentId}:`, error);
      throw error;
    }
  }

  /**
   * Split text into chunks
   */
  private splitTextIntoChunks(text: string, chunkSize: number): string[] {
    const chunks: string[] = [];
    const sentences = text.split(/[.!?]+/);
    let currentChunk = '';

    for (const sentence of sentences) {
      if ((currentChunk + sentence).length > chunkSize && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence;
      } else {
        currentChunk += sentence + '.';
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk.length > 10); // Filter out very small chunks
  }

  /**
   * Get collection stats
   */
  async getStats(): Promise<any> {
    try {
      const count = await this.collection.count();
      return {
        totalDocuments: count,
        collectionName: this.collection.name
      };
    } catch (error) {
      console.error('❌ Failed to get stats:', error);
      return { totalDocuments: 0, collectionName: 'unknown' };
    }
  }
}
