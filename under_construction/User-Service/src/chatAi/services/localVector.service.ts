import {
  ChromaApi,
  OpenAIEmbeddingFunction,
  Collection,
  AdminClient,
} from 'chromadb';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LocalVectorService {
  private client: ChromaApi;
  private adminClient: AdminClient;
  private embeddingFunction: OpenAIEmbeddingFunction;
  private collections: Map<string, Collection> = new Map();

  constructor() {
    this.initializeChroma();
  }

  private async initializeChroma() {
    try {
      // Initialize Chroma client (local instance)
      this.client = new ChromaApi({
        path: process.env.CHROMA_DB_PATH || './data/chroma_db',
      });

      // Initialize admin client for tenant/database management
      this.adminClient = new AdminClient({
        path: process.env.CHROMA_DB_PATH || './data/chroma_db',
      });

      // Initialize embedding function
      this.embeddingFunction = new OpenAIEmbeddingFunction({
        openai_api_key: process.env.OPENAI_API_KEY,
        openai_model: 'text-embedding-ada-002',
      });

      console.log(
        '✅ Local Vector DB (Chroma) with multitenancy initialized successfully',
      );
    } catch (error) {
      console.error('❌ Failed to initialize Chroma DB:', error);
      throw error;
    }
  }

  /**
   * Get or create tenant and database for proper isolation
   */
  private async getOrCreateTenantAndDatabase(
    userId: string,
    appId: string,
  ): Promise<{ tenant: string; database: string }> {
    // Strategy: App-per-Database approach for balanced isolation
    const tenant = 'chatai_system';
    const database = `app_${appId}`;

    try {
      // Check if tenant exists, create if not
      try {
        await this.adminClient.getTenant(tenant);
      } catch (error) {
        await this.adminClient.createTenant(tenant);
        console.log(`✅ Created tenant: ${tenant}`);
      }

      // Check if database exists, create if not
      try {
        await this.adminClient.getDatabase(database, tenant);
      } catch (error) {
        await this.adminClient.createDatabase(database, tenant);
        console.log(`✅ Created database: ${database} in tenant: ${tenant}`);
      }

      return { tenant, database };
    } catch (error) {
      console.error('❌ Failed to setup tenant/database:', error);
      throw error;
    }
  }

  /**
   * Get collection with proper tenant/database isolation
   */
  private async getCollection(
    userId: string,
    appId: string,
    documentId?: string,
  ): Promise<Collection> {
    const { tenant, database } = await this.getOrCreateTenantAndDatabase(
      userId,
      appId,
    );

    // Collection naming: user-specific within app database
    const collectionName = `user_${userId}_docs`;
    const collectionKey = `${tenant}_${database}_${collectionName}`;

    // Check if collection is already cached
    if (this.collections.has(collectionKey)) {
      return this.collections.get(collectionKey)!;
    }

    try {
      // Create client with specific tenant and database
      const isolatedClient = new ChromaApi({
        path: process.env.CHROMA_DB_PATH || './data/chroma_db',
        tenant,
        database,
      });

      // Get or create collection
      const collection = await isolatedClient.getOrCreateCollection({
        name: collectionName,
        embeddingFunction: this.embeddingFunction,
      });

      // Cache the collection
      this.collections.set(collectionKey, collection);

      console.log(
        `✅ Collection ready: ${collectionName} (tenant: ${tenant}, db: ${database})`,
      );
      return collection;
    } catch (error) {
      console.error(
        `❌ Failed to get collection for user ${userId}, app ${appId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Add document to local vector database with proper isolation
   */
  async addDocument(
    documentId: string,
    parsedText: string,
    metadata: any,
    userId: string,
    appId: string,
  ): Promise<string> {
    try {
      console.log(
        `📝 Adding document ${documentId} to local vector DB (user: ${userId}, app: ${appId})`,
      );

      // Get isolated collection for this user and app
      const collection = await this.getCollection(userId, appId, documentId);

      // Split text into chunks (simple implementation)
      const chunks = this.splitTextIntoChunks(parsedText, 1000);

      const ids: string[] = [];
      const documents: string[] = [];
      const metadatas: any[] = [];

      chunks.forEach((chunk, index) => {
        const chunkId = `${documentId}_chunk_${index}`;
        ids.push(chunkId);
        documents.push(chunk);
        metadatas.push({
          ...metadata,
          documentId,
          chunkIndex: index,
          chunkId,
          userId,
          appId,
          addedAt: new Date().toISOString(),
        });
      });

      // Add to isolated collection
      await collection.add({
        ids,
        documents,
        metadatas,
      });

      console.log(
        `✅ Added ${chunks.length} chunks for document ${documentId} (isolated)`,
      );
      return `local_${documentId}`;
    } catch (error) {
      console.error(`❌ Failed to add document ${documentId}:`, error);
      throw error;
    }
  }

  /**
   * Query local vector database with proper isolation
   */
  async queryDocuments(
    query: string,
    userId: string,
    appId: string,
    documentIds?: string[],
    topK: number = 5,
  ): Promise<any> {
    try {
      console.log(
        `🔍 Querying local vector DB: "${query.substring(0, 50)}..." (user: ${userId}, app: ${appId})`,
      );

      // Get isolated collection for this user and app
      const collection = await this.getCollection(userId, appId);

      const whereClause = documentIds
        ? {
            documentId: { $in: documentIds },
            userId: userId, // Additional isolation check
            appId: appId, // Additional isolation check
          }
        : {
            userId: userId, // Ensure only user's documents are queried
            appId: appId, // Ensure only app's documents are queried
          };

      const results = await collection.query({
        queryTexts: [query],
        nResults: topK,
        where: whereClause,
      });

      console.log(
        `✅ Found ${results.documents[0]?.length || 0} relevant chunks (isolated)`,
      );

      return {
        nodes:
          results.documents[0]?.map((doc, index) => ({
            text: doc,
            score: results.distances?.[0]?.[index] || 0,
            metadata: results.metadatas?.[0]?.[index] || {},
          })) || [],
      };
    } catch (error) {
      console.error('❌ Failed to query local vector DB:', error);
      throw error;
    }
  }

  /**
   * Delete document from local vector database
   */
  async deleteDocument(documentId: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting document ${documentId} from local vector DB`);

      await this.collection.delete({
        where: { documentId },
      });

      console.log(`✅ Deleted document ${documentId} from local vector DB`);
    } catch (error) {
      console.error(`❌ Failed to delete document ${documentId}:`, error);
      throw error;
    }
  }

  /**
   * Split text into chunks
   */
  private splitTextIntoChunks(text: string, chunkSize: number): string[] {
    const chunks: string[] = [];
    const sentences = text.split(/[.!?]+/);
    let currentChunk = '';

    for (const sentence of sentences) {
      if ((currentChunk + sentence).length > chunkSize && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence;
      } else {
        currentChunk += sentence + '.';
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter((chunk) => chunk.length > 10); // Filter out very small chunks
  }

  /**
   * Get collection stats
   */
  async getStats(): Promise<any> {
    try {
      const count = await this.collection.count();
      return {
        totalDocuments: count,
        collectionName: this.collection.name,
      };
    } catch (error) {
      console.error('❌ Failed to get stats:', error);
      return { totalDocuments: 0, collectionName: 'unknown' };
    }
  }
}
