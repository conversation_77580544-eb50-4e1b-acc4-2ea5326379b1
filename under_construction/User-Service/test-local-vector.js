/**
 * Test script for Local Vector Database functionality
 * Run this to test if Chroma DB integration is working
 */

const { ChromaApi, OpenAIEmbeddingFunction } = require('chromadb');

async function testLocalVectorDB() {
  console.log('🧪 Testing Local Vector Database...');

  try {
    // Initialize Chroma client
    const client = new ChromaApi({
      path: './data/chroma_db'
    });

    console.log('✅ Chroma client initialized');

    // Test embedding function (you'll need to add your OpenAI API key)
    const embeddingFunction = new OpenAIEmbeddingFunction({
      openai_api_key: process.env.OPENAI_API_KEY || 'your_openai_api_key_here',
      openai_model: "text-embedding-ada-002"
    });

    console.log('✅ Embedding function initialized');

    // Create or get test collection
    const collection = await client.getOrCreateCollection({
      name: "test_collection",
      embeddingFunction: embeddingFunction,
    });

    console.log('✅ Test collection created/retrieved');

    // Test data
    const testDocuments = [
      "This is a test document about artificial intelligence and machine learning.",
      "Vector databases are essential for semantic search and RAG applications.",
      "Chroma DB is an open-source vector database for AI applications."
    ];

    const testIds = ["doc1", "doc2", "doc3"];
    const testMetadata = [
      { source: "test", type: "ai" },
      { source: "test", type: "database" },
      { source: "test", type: "chroma" }
    ];

    // Add documents to collection
    await collection.add({
      ids: testIds,
      documents: testDocuments,
      metadatas: testMetadata
    });

    console.log('✅ Test documents added to collection');

    // Test query
    const queryResults = await collection.query({
      queryTexts: ["What is vector database?"],
      nResults: 2
    });

    console.log('✅ Query executed successfully');
    console.log('📊 Query Results:');
    console.log('Documents:', queryResults.documents[0]);
    console.log('Distances:', queryResults.distances[0]);
    console.log('Metadata:', queryResults.metadatas[0]);

    // Get collection stats
    const count = await collection.count();
    console.log(`📈 Collection contains ${count} documents`);

    // Clean up test collection
    await client.deleteCollection({ name: "test_collection" });
    console.log('🧹 Test collection cleaned up');

    console.log('🎉 Local Vector Database test completed successfully!');

  } catch (error) {
    console.error('❌ Local Vector Database test failed:', error);
    
    if (error.message.includes('openai_api_key')) {
      console.log('💡 Tip: Make sure to set your OPENAI_API_KEY in the .env file');
    }
    
    if (error.message.includes('ENOENT') || error.message.includes('permission')) {
      console.log('💡 Tip: Make sure the ./data/chroma_db directory is writable');
    }
  }
}

// Run the test
testLocalVectorDB();
